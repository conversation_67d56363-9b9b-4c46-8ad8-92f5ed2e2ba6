/**
 * Performance optimization utilities
 * Централизованное управление производительностью приложения
 */

// Кэш для DOM элементов
const domCache = new Map();

// Кэш для вычислений
const computationCache = new Map();

// Debounce функция с кэшированием
const debouncedFunctions = new Map();

/**
 * Оптимизированное кэширование DOM элементов
 */
export function getCachedElement(selector) {
  if (!domCache.has(selector)) {
    const element = document.querySelector(selector);
    if (element) {
      domCache.set(selector, element);
    }
  }
  return domCache.get(selector);
}

/**
 * Очистка кэша DOM элементов
 */
export function clearDOMCache() {
  domCache.clear();
}

/**
 * Оптимизированный debounce с кэшированием
 */
export function getDebounced(key, fn, delay = 300) {
  if (!debouncedFunctions.has(key)) {
    let timeout;
    const debouncedFn = function (...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => fn.apply(this, args), delay);
    };
    debouncedFunctions.set(key, debouncedFn);
  }
  return debouncedFunctions.get(key);
}

/**
 * Кэширование результатов вычислений
 */
export function getCachedComputation(key, computeFn) {
  if (!computationCache.has(key)) {
    const result = computeFn();
    computationCache.set(key, result);
  }
  return computationCache.get(key);
}

/**
 * Очистка кэша вычислений
 */
export function clearComputationCache(key = null) {
  if (key) {
    computationCache.delete(key);
  } else {
    computationCache.clear();
  }
}

/**
 * Оптимизированный requestAnimationFrame с приоритетами
 */
const animationQueue = {
  high: [],
  normal: [],
  low: [],
};

let isProcessing = false;

export function scheduleAnimation(callback, priority = 'normal') {
  animationQueue[priority].push(callback);

  if (!isProcessing) {
    isProcessing = true;
    requestAnimationFrame(processAnimationQueue);
  }
}

function processAnimationQueue() {
  // Обрабатываем в порядке приоритета
  const queues = [animationQueue.high, animationQueue.normal, animationQueue.low];

  for (const queue of queues) {
    while (queue.length > 0) {
      const callback = queue.shift();
      try {
        callback();
      } catch (error) {
        console.error('Animation callback error:', error);
      }
    }
  }

  isProcessing = false;
}

/**
 * Оптимизация изображений - lazy loading
 */
export function setupLazyLoading() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01,
      }
    );

    // Наблюдаем за всеми изображениями с data-src
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });

    return imageObserver;
  }
}

/**
 * Мониторинг производительности
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
  }

  startMeasure(name) {
    this.metrics.set(name, performance.now());
  }

  endMeasure(name) {
    const startTime = this.metrics.get(name);
    if (startTime) {
      const duration = performance.now() - startTime;
      console.log(`⚡ ${name}: ${duration.toFixed(2)}ms`);
      this.metrics.delete(name);
      return duration;
    }
  }

  // Мониторинг FPS
  startFPSMonitoring() {
    let frames = 0;
    let lastTime = performance.now();

    const countFPS = () => {
      frames++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime));
        console.log(`🎯 FPS: ${fps}`);
        frames = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(countFPS);
    };

    requestAnimationFrame(countFPS);
  }

  // Мониторинг памяти (если доступно)
  logMemoryUsage() {
    if (performance.memory) {
      const memory = performance.memory;
      console.log(
        `💾 Memory: ${Math.round(memory.usedJSHeapSize / 1048576)}MB / ${Math.round(memory.totalJSHeapSize / 1048576)}MB`
      );
    }
  }
}

// Глобальный экземпляр монитора
export const performanceMonitor = new PerformanceMonitor();

/**
 * Оптимизация событий - пассивные слушатели
 */
export function addOptimizedEventListener(element, event, handler, options = {}) {
  const optimizedOptions = {
    passive: true,
    ...options,
  };

  element.addEventListener(event, handler, optimizedOptions);

  return () => element.removeEventListener(event, handler, optimizedOptions);
}

/**
 * Батчинг DOM операций
 */
export class DOMBatcher {
  constructor() {
    this.readOperations = [];
    this.writeOperations = [];
    this.scheduled = false;
  }

  read(operation) {
    this.readOperations.push(operation);
    this.schedule();
  }

  write(operation) {
    this.writeOperations.push(operation);
    this.schedule();
  }

  schedule() {
    if (!this.scheduled) {
      this.scheduled = true;
      requestAnimationFrame(() => this.flush());
    }
  }

  flush() {
    // Сначала все чтения
    this.readOperations.forEach(op => op());
    this.readOperations = [];

    // Затем все записи
    this.writeOperations.forEach(op => op());
    this.writeOperations = [];

    this.scheduled = false;
  }
}

// Глобальный экземпляр батчера
export const domBatcher = new DOMBatcher();

/**
 * Инициализация всех оптимизаций
 */
export function initPerformanceOptimizations() {
  console.log('🚀 Initializing performance optimizations...');

  // Настройка lazy loading для изображений
  setupLazyLoading();

  // Запуск мониторинга производительности в dev режиме
  if (import.meta.env.DEV) {
    performanceMonitor.startFPSMonitoring();

    // Логируем использование памяти каждые 30 секунд
    setInterval(() => {
      performanceMonitor.logMemoryUsage();
    }, 30000);
  }

  console.log('✅ Performance optimizations initialized');
}
